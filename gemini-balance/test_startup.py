#!/usr/bin/env python3
"""
测试应用启动的简单脚本
"""

import os
import sys
import traceback

def test_imports():
    """测试所有必要的导入"""
    try:
        print("🔍 Testing imports...")
        
        # 测试基本导入
        from dotenv import load_dotenv
        print("✅ dotenv imported successfully")
        
        # 加载环境变量
        load_dotenv()
        print("✅ Environment variables loaded")
        
        # 测试应用导入
        from app.core.application import create_app
        print("✅ Application module imported successfully")
        
        from app.log.logger import get_main_logger
        print("✅ Logger module imported successfully")
        
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        traceback.print_exc()
        return False

def test_app_creation():
    """测试应用创建"""
    try:
        print("\n🏗️ Testing app creation...")
        
        from app.core.application import create_app
        app = create_app()
        print("✅ FastAPI app created successfully")
        print(f"📋 App title: {app.title}")
        print(f"📋 App version: {app.version}")
        
        return True, app
    except Exception as e:
        print(f"❌ App creation failed: {e}")
        traceback.print_exc()
        return False, None

def test_environment():
    """测试环境变量"""
    print("\n🌍 Testing environment variables...")
    
    important_vars = [
        'DATABASE_TYPE',
        'API_KEYS', 
        'ALLOWED_TOKENS',
        'PORT'
    ]
    
    for var in important_vars:
        value = os.getenv(var)
        if value:
            # 不显示敏感信息的完整内容
            if 'KEY' in var or 'TOKEN' in var:
                display_value = f"{value[:10]}..." if len(value) > 10 else value
            else:
                display_value = value
            print(f"✅ {var}: {display_value}")
        else:
            print(f"⚠️  {var}: Not set")

def main():
    """主测试函数"""
    print("🧪 Gemini Balance Startup Test")
    print("=" * 40)
    
    # 测试环境变量
    test_environment()
    
    # 测试导入
    if not test_imports():
        sys.exit(1)
    
    # 测试应用创建
    success, app = test_app_creation()
    if not success:
        sys.exit(1)
    
    print("\n🎉 All tests passed! App should start successfully.")
    
    # 显示启动命令
    port = os.getenv("PORT", "8000")
    print(f"\n🚀 To start the app:")
    print(f"uvicorn app.main:app --host 0.0.0.0 --port {port}")

if __name__ == "__main__":
    main()
