#!/usr/bin/env python3
"""
最简单的测试应用，用于验证 Zeabur 部署配置
"""

import os
import uvicorn
from fastapi import FastAPI

# 创建简单的 FastAPI 应用
app = FastAPI(title="Gemini Balance Test")

@app.get("/")
async def root():
    return {
        "message": "Hello from Gemini Balance!", 
        "port": os.getenv("PORT", "8000"),
        "status": "running"
    }

@app.get("/health")
async def health():
    return {
        "status": "healthy", 
        "port": os.getenv("PORT", "8000"),
        "env_vars": {
            "DATABASE_TYPE": os.getenv("DATABASE_TYPE"),
            "API_KEYS_SET": bool(os.getenv("API_KEYS")),
            "ALLOWED_TOKENS_SET": bool(os.getenv("ALLOWED_TOKENS")),
        }
    }

@app.get("/env")
async def env_info():
    return {
        "PORT": os.getenv("PORT"),
        "DATABASE_TYPE": os.getenv("DATABASE_TYPE"),
        "API_KEYS": os.getenv("API_KEYS", "Not set")[:50] + "..." if os.getenv("API_KEYS") else "Not set",
        "ALLOWED_TOKENS": os.getenv("ALLOWED_TOKENS", "Not set")[:50] + "..." if os.getenv("ALLOWED_TOKENS") else "Not set",
    }

if __name__ == "__main__":
    port = int(os.getenv("PORT", 8000))
    print(f"🚀 Starting simple test app on port {port}")
    print(f"📊 Environment variables:")
    print(f"   PORT: {os.getenv('PORT', 'Not set')}")
    print(f"   DATABASE_TYPE: {os.getenv('DATABASE_TYPE', 'Not set')}")
    print(f"   API_KEYS: {'Set' if os.getenv('API_KEYS') else 'Not set'}")
    uvicorn.run(app, host="0.0.0.0", port=port)
