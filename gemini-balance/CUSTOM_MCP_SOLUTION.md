# 自定義 Gemini MCP 服務器解決方案

## 🎯 目標

創建一個自定義的 MCP 服務器，作為 gemini-balance 代理服務的包裝器，讓 Augment 可以通過 MCP 使用你的負載均衡代理。

## 📋 方案概述

```
Augment → MCP 服務器 → gemini-balance 代理 → 多個 Gemini API Keys
```

## 🔧 實現步驟

### 1. 創建自定義 MCP 服務器

創建一個 Node.js MCP 服務器，將請求轉發到你的 gemini-balance 代理：

```javascript
// gemini-proxy-mcp-server.js
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';

const server = new Server(
  {
    name: 'gemini-proxy-server',
    version: '1.0.0',
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

// 定義工具
server.setRequestHandler('tools/list', async () => {
  return {
    tools: [
      {
        name: 'gemini_chat',
        description: 'Chat with <PERSON> using load-balanced API keys',
        inputSchema: {
          type: 'object',
          properties: {
            message: {
              type: 'string',
              description: 'The message to send to Gemini'
            },
            model: {
              type: 'string',
              description: 'The Gemini model to use',
              default: 'gemini-1.5-flash'
            }
          },
          required: ['message']
        }
      }
    ]
  };
});

// 處理工具調用
server.setRequestHandler('tools/call', async (request) => {
  const { name, arguments: args } = request.params;
  
  if (name === 'gemini_chat') {
    try {
      const response = await fetch('http://localhost:8001/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer sk-gemini-proxy-token-123456',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: args.model || 'gemini-1.5-flash',
          messages: [{ role: 'user', content: args.message }],
          max_tokens: 1000
        })
      });
      
      const data = await response.json();
      
      return {
        content: [
          {
            type: 'text',
            text: data.choices[0].message.content
          }
        ]
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Error: ${error.message}`
          }
        ],
        isError: true
      };
    }
  }
  
  throw new Error(`Unknown tool: ${name}`);
});

// 啟動服務器
async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
}

main().catch(console.error);
```

### 2. 創建 package.json

```json
{
  "name": "gemini-proxy-mcp-server",
  "version": "1.0.0",
  "type": "module",
  "main": "gemini-proxy-mcp-server.js",
  "dependencies": {
    "@modelcontextprotocol/sdk": "^1.0.0"
  },
  "bin": {
    "gemini-proxy-mcp-server": "./gemini-proxy-mcp-server.js"
  }
}
```

### 3. 在 Augment 中配置

在 Augment 的 settings.json 中添加：

```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "gemini-proxy",
        "command": "node",
        "args": ["/path/to/gemini-proxy-mcp-server.js"]
      }
    ]
  }
}
```

## ⚡ 優勢

1. **完全整合** - Augment 可以直接使用 MCP 協議
2. **負載均衡** - 保持你的多 API Key 負載均衡功能
3. **透明代理** - Augment 不知道背後有代理，使用體驗一致
4. **可擴展** - 可以添加更多功能（如模型選擇、參數調整等）

## 🚧 限制

1. **需要開發** - 需要創建和維護自定義 MCP 服務器
2. **額外複雜性** - 增加了一個中間層
3. **調試難度** - 多層架構可能增加調試複雜性

## 🔄 替代方案

### 方案二：直接使用現有 MCP 服務器

如果你不想創建自定義服務器，可以：

1. **使用官方 Gemini API** - 直接在 Augment 中配置 Gemini API
2. **輪詢使用不同 Key** - 手動切換不同的 API Key
3. **等待官方支援** - 等待官方 Gemini MCP 服務器

### 方案三：修改 gemini-balance 支援 MCP

修改你的 gemini-balance 服務，讓它同時支援 MCP 協議：

1. 添加 MCP 端點
2. 實現 MCP 協議處理
3. 保持現有的 HTTP API 功能

## 💡 建議

考慮到開發成本和維護複雜性，我建議：

1. **短期**：直接在 Augment 中配置單個 Gemini API Key
2. **中期**：如果需要負載均衡，實現方案一的自定義 MCP 服務器
3. **長期**：關注官方是否會推出 Gemini MCP 服務器

你的 gemini-balance 代理服務本身已經非常有價值，可以用於：
- 直接 API 調用
- 其他支援自定義端點的工具
- 未來可能支援自定義端點的 CLI 工具
