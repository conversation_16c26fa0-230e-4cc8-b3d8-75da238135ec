# Gemini Balance 設置指南

## 🚀 服務已成功部署！

你的 gemini-balance 代理服務現在運行在：
- **服務地址**: `http://localhost:8001`
- **管理面板**: `http://localhost:8001/keys_status`
- **授權 Token**: `sk-gemini-proxy-token-123456`

## 📝 下一步配置

### 1. 添加你的 Gemini API Keys

編輯 `.env` 檔案中的 `API_KEYS` 設置：

```bash
# 替換為你的實際 API Keys
API_KEYS=["AIzaSy_YOUR_FIRST_API_KEY_HERE","AIzaSy_YOUR_SECOND_API_KEY_HERE","AIzaSy_YOUR_THIRD_API_KEY_HERE"]
```

### 2. 配置 Gemini CLI 使用代理

如果你使用 Gemini CLI，可以設置環境變數：

```bash
# 設置代理 URL
export GEMINI_API_BASE_URL="http://localhost:8001/v1beta"
export GEMINI_API_KEY="sk-gemini-proxy-token-123456"
```

或者在 CLI 命令中指定：

```bash
gemini-cli --api-base-url http://localhost:8001/v1beta --api-key sk-gemini-proxy-token-123456
```

### 3. 配置 OpenAI 格式客戶端

對於支援 OpenAI API 格式的客戶端：

```bash
# OpenAI 格式端點
export OPENAI_API_BASE="http://localhost:8001/v1"
export OPENAI_API_KEY="sk-gemini-proxy-token-123456"
```

### 4. 配置 MCP 服務

如果你要使用 Gemini MCP，可以在 MCP 配置中設置：

```json
{
  "mcpServers": {
    "gemini": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-gemini"],
      "env": {
        "GEMINI_API_KEY": "sk-gemini-proxy-token-123456",
        "GEMINI_API_BASE_URL": "http://localhost:8001/v1beta"
      }
    }
  }
}
```

## 🔧 API 端點

### Gemini 格式
- **Base URL**: `http://localhost:8001/v1beta`
- **模型列表**: `GET /v1beta/models`
- **生成內容**: `POST /v1beta/models/{model}:generateContent`

### OpenAI 格式
- **Base URL**: `http://localhost:8001/v1`
- **模型列表**: `GET /v1/models`
- **聊天完成**: `POST /v1/chat/completions`
- **嵌入**: `POST /v1/embeddings`

## 📊 監控和管理

訪問管理面板查看 API Key 狀態：
```bash
curl -H "Authorization: Bearer sk-gemini-proxy-token-123456" http://localhost:8001/keys_status
```

## 🔄 重啟服務

如果需要重啟服務：

```bash
# 停止當前服務 (Ctrl+C)
# 然後重新啟動
cd gemini-balance
source venv/bin/activate
python -m uvicorn app.main:app --host 0.0.0.0 --port 8001
```

## ⚡ 優勢

通過這個代理服務，你可以：
1. **負載均衡**: 多個 API Key 自動輪詢
2. **統一接口**: 同時支援 Gemini 和 OpenAI 格式
3. **監控管理**: 實時查看 Key 狀態和使用情況
4. **容錯處理**: 自動重試和故障轉移
5. **無限使用**: 通過多個帳號實現更高的使用額度
