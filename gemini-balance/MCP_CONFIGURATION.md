# 配置 Gemini MCP 使用 gemini-balance 代理

## 🎯 目標

將 Gemini MCP 服務配置為通過我們的 gemini-balance 代理服務來存取 Gemini API，實現負載均衡和統一管理。

## 📋 前提條件

1. ✅ gemini-balance 服務已運行在 `http://localhost:8001`
2. ✅ 已配置授權 Token: `sk-gemini-proxy-token-123456`
3. 需要安裝 Gemini MCP 服務

## 🔧 方法一：使用官方 Gemini MCP 服務

### 1. 安裝 Gemini MCP 服務

```bash
# 安裝官方 Gemini MCP 服務
npm install -g @modelcontextprotocol/server-gemini
```

### 2. 配置 Claude Desktop 或 Augment

在你的 MCP 配置文件中添加以下配置：

**對於 Claude Desktop** (`~/Library/Application Support/Claude/claude_desktop_config.json`):

```json
{
  "mcpServers": {
    "gemini-proxy": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-gemini"],
      "env": {
        "GEMINI_API_KEY": "sk-gemini-proxy-token-123456",
        "GEMINI_API_BASE_URL": "http://localhost:8001/v1beta"
      }
    }
  }
}
```

**對於 Augment** (在 Augment 設置中):

```json
{
  "mcpServers": {
    "gemini-proxy": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-gemini"],
      "env": {
        "GEMINI_API_KEY": "sk-gemini-proxy-token-123456",
        "GEMINI_API_BASE_URL": "http://localhost:8001/v1beta"
      }
    }
  }
}
```

## 🔧 方法二：使用 Gemini Grounding MCP 服務

### 1. 安裝 Gemini Grounding MCP

```bash
# 克隆並構建
git clone https://github.com/ml0-1337/mcp-gemini-grounding.git
cd mcp-gemini-grounding
make build
make install
```

### 2. 配置使用代理

```json
{
  "mcpServers": {
    "gemini-grounding-proxy": {
      "command": "mcp-gemini-grounding",
      "env": {
        "GEMINI_API_KEY": "sk-gemini-proxy-token-123456",
        "GEMINI_API_BASE_URL": "http://localhost:8001/v1beta"
      },
      "timeout": 30000
    }
  }
}
```

## 🔧 方法三：自定義 MCP 服務配置

如果你需要更靈活的配置，可以創建自定義的環境變數：

### 1. 設置環境變數

```bash
# 設置代理相關環境變數
export GEMINI_PROXY_API_KEY="sk-gemini-proxy-token-123456"
export GEMINI_PROXY_BASE_URL="http://localhost:8001/v1beta"
export GEMINI_PROXY_OPENAI_URL="http://localhost:8001/v1"
```

### 2. 配置 MCP 服務

```json
{
  "mcpServers": {
    "gemini-proxy": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-gemini"],
      "env": {
        "GEMINI_API_KEY": "$GEMINI_PROXY_API_KEY",
        "GEMINI_API_BASE_URL": "$GEMINI_PROXY_BASE_URL"
      }
    }
  }
}
```

## 🧪 測試配置

### 1. 檢查 MCP 服務狀態

```bash
# 檢查 gemini-balance 服務
curl -H "Authorization: Bearer sk-gemini-proxy-token-123456" \
     http://localhost:8001/health

# 檢查模型列表
curl -H "Authorization: Bearer sk-gemini-proxy-token-123456" \
     http://localhost:8001/v1beta/models
```

### 2. 測試 MCP 連接

在 Claude Desktop 或 Augment 中，你應該能夠：
- 看到 Gemini 相關的工具和功能
- 使用 Gemini 模型進行對話
- 利用 Google 搜索功能（如果使用 Gemini Grounding）

## 🔄 故障排除

### 常見問題

1. **MCP 服務無法啟動**
   - 檢查 Node.js 是否已安裝
   - 確認 gemini-balance 服務正在運行
   - 檢查端口 8001 是否可訪問

2. **認證失敗**
   - 確認 Token `sk-gemini-proxy-token-123456` 正確
   - 檢查 gemini-balance 的 `.env` 配置

3. **API 調用失敗**
   - 確認已在 gemini-balance 中配置真實的 Gemini API Keys
   - 檢查 API Key 是否有效且有足夠額度

### 調試命令

```bash
# 檢查 MCP 服務日誌
# (具體方法取決於你使用的 MCP 客戶端)

# 檢查 gemini-balance 日誌
# 查看運行 gemini-balance 的終端輸出

# 測試直接 API 調用
curl -X POST \
  -H "Authorization: Bearer sk-gemini-proxy-token-123456" \
  -H "Content-Type: application/json" \
  -d '{"contents":[{"parts":[{"text":"Hello"}]}]}' \
  http://localhost:8001/v1beta/models/gemini-1.5-flash:generateContent
```

## ✅ 完成後的效果

配置成功後，你將獲得：

1. **統一的 API 管理**: 所有 Gemini API 調用通過 gemini-balance 代理
2. **負載均衡**: 多個 API Key 自動輪詢使用
3. **監控能力**: 通過管理面板查看使用情況
4. **容錯處理**: 自動重試和故障轉移
5. **無限使用**: 通過多個帳號實現更高的使用額度

現在你的 MCP 服務將通過 gemini-balance 代理來存取 Gemini API，實現了真正的"無限使用"效果！
