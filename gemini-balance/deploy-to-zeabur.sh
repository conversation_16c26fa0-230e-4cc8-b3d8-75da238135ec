#!/bin/bash

echo "🚀 部署到 Zeabur"
echo "=================="

# 检查是否在正确的目录
if [ ! -f "Dockerfile" ]; then
    echo "❌ 错误：请在 gemini-balance 目录中运行此脚本"
    exit 1
fi

# 检查 git 状态
if [ ! -d ".git" ]; then
    echo "📦 初始化 Git 仓库..."
    git init
    git branch -M main
fi

# 添加所有更改
echo "📝 添加更改到 Git..."
git add .

# 提交更改
echo "💾 提交更改..."
git commit -m "Fix Zeabur port configuration

- Update Dockerfile to use dynamic PORT environment variable
- Fix docker-start.sh to use app.main:app instead of test_app
- Remove test_app.py file
- Ensure proper port binding for Zeabur deployment"

# 推送到远程仓库（如果已配置）
if git remote get-url origin >/dev/null 2>&1; then
    echo "🚀 推送到远程仓库..."
    git push origin main
    echo "✅ 代码已推送！Zeabur 应该会自动重新部署。"
else
    echo "⚠️  未配置远程仓库。请手动推送到 GitHub："
    echo "   git remote add origin https://github.com/YOUR_USERNAME/gemini-balance.git"
    echo "   git push -u origin main"
fi

echo ""
echo "🔧 部署后测试命令："
echo "curl https://gemini-balance-gmgm.zeabur.app/health"
echo ""
echo "📊 管理面板："
echo "https://gemini-balance-gmgm.zeabur.app/keys_status"
