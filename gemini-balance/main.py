#!/usr/bin/env python3
"""
主入口文件，用于 Zeabur 自动检测
"""

import os
import uvicorn
from simple_test import app

if __name__ == "__main__":
    port = int(os.getenv("PORT", 8000))
    print(f"🚀 Starting Gemini Balance on port {port}")
    print(f"📊 Environment variables:")
    print(f"   PORT: {os.getenv('PORT', 'Not set')}")
    print(f"   DATABASE_TYPE: {os.getenv('DATABASE_TYPE', 'Not set')}")
    print(f"   API_KEYS: {'Set' if os.getenv('API_KEYS') else 'Not set'}")
    uvicorn.run(app, host="0.0.0.0", port=port)
