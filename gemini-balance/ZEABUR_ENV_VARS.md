# 🔧 Zeabur 環境變數配置

## 📋 複製以下環境變數到 Zeabur

在 Zeabur 的環境變數設置中，逐一添加以下變數：

### 🔑 核心配置（必需）

```bash
DATABASE_TYPE=sqlite
```

```bash
SQLITE_DATABASE=gemini_balance.db
```

```bash
API_KEYS=["AIzaSyAqcX0Y05HkcWaWeYeCRzHdaKngd1CoLhw","AIzaSyCfJXf641hGNYAfXmZVmxtBPQfxagcbcxE","AIzaSyBOYU7QHQTg_l7e5ZDHLMG76rJF0NH97-0","AIzaSyA8pJifiXAbo6uA6_s0pO2Ns6W9DoShKEg","AIzaSyB5MDpa1BGcfeA-EHt1zNA20LPz3aLuoTU"]
```

```bash
ALLOWED_TOKENS=["sk-gemini-proxy-token-123456"]
```

```bash
AUTH_TOKEN=sk-gemini-proxy-token-123456
```

```bash
TEST_MODEL=gemini-1.5-flash
```

```bash
BASE_URL=https://generativelanguage.googleapis.com/v1beta
```

```bash
PORT=8000
```

### ⚙️ 模型配置

```bash
IMAGE_MODELS=["gemini-2.0-flash-exp"]
```

```bash
SEARCH_MODELS=["gemini-2.0-flash-exp"]
```

```bash
FILTERED_MODELS=["gemini-1.0-pro-vision-latest","gemini-pro-vision","chat-bison-001","text-bison-001","embedding-gecko-001"]
```

### 🔧 性能配置

```bash
MAX_FAILURES=5
```

```bash
MAX_RETRIES=3
```

```bash
CHECK_INTERVAL_HOURS=1
```

```bash
TIMEZONE=Asia/Shanghai
```

```bash
TIME_OUT=300
```

```bash
PROXIES=[]
```

### 🎛️ 功能開關

```bash
TOOLS_CODE_EXECUTION_ENABLED=false
```

```bash
SHOW_SEARCH_LINK=true
```

```bash
SHOW_THINKING_PROCESS=true
```

```bash
URL_NORMALIZATION_ENABLED=false
```

### 📊 流式輸出優化

```bash
STREAM_OPTIMIZER_ENABLED=true
```

```bash
STREAM_MIN_DELAY=0.016
```

```bash
STREAM_MAX_DELAY=0.024
```

```bash
STREAM_SHORT_TEXT_THRESHOLD=10
```

```bash
STREAM_LONG_TEXT_THRESHOLD=50
```

```bash
STREAM_CHUNK_SIZE=5
```

### 🔄 假流式配置

```bash
FAKE_STREAM_ENABLED=false
```

```bash
FAKE_STREAM_EMPTY_DATA_INTERVAL_SECONDS=5
```

### 📝 日誌配置

```bash
LOG_LEVEL=info
```

```bash
AUTO_DELETE_ERROR_LOGS_ENABLED=true
```

```bash
AUTO_DELETE_ERROR_LOGS_DAYS=7
```

```bash
AUTO_DELETE_REQUEST_LOGS_ENABLED=false
```

```bash
AUTO_DELETE_REQUEST_LOGS_DAYS=30
```

### 🛡️ 安全設置

```bash
SAFETY_SETTINGS=[{"category": "HARM_CATEGORY_HARASSMENT", "threshold": "OFF"}, {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "OFF"}, {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "OFF"}, {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "OFF"}, {"category": "HARM_CATEGORY_CIVIC_INTEGRITY", "threshold": "BLOCK_NONE"}]
```

## 🚀 快速設置指南

### 最小配置（只需這 8 個變數就能運行）

如果你想快速開始，只需要設置這些核心變數：

1. `DATABASE_TYPE=sqlite`
2. `SQLITE_DATABASE=gemini_balance.db`
3. `API_KEYS=["AIzaSyAqcX0Y05HkcWaWeYeCRzHdaKngd1CoLhw","AIzaSyCfJXf641hGNYAfXmZVmxtBPQfxagcbcxE","AIzaSyBOYU7QHQTg_l7e5ZDHLMG76rJF0NH97-0","AIzaSyA8pJifiXAbo6uA6_s0pO2Ns6W9DoShKEg","AIzaSyB5MDpa1BGcfeA-EHt1zNA20LPz3aLuoTU"]`
4. `ALLOWED_TOKENS=["sk-gemini-proxy-token-123456"]`
5. `AUTH_TOKEN=sk-gemini-proxy-token-123456`
6. `TEST_MODEL=gemini-1.5-flash`
7. `BASE_URL=https://generativelanguage.googleapis.com/v1beta`
8. `PORT=8000`

## 📝 設置步驟

1. **在 Zeabur 中**：
   - 前往你的服務設置
   - 點擊 "Environment Variables"
   - 逐一添加上面的變數

2. **重要提醒**：
   - 變數名稱和值都要完全一致
   - JSON 格式的值（如 API_KEYS）要保持完整的格式
   - 不要添加額外的引號或空格

3. **保存並重新部署**：
   - 添加完所有變數後
   - 點擊 "Save"
   - 服務會自動重新部署

## ✅ 驗證部署

部署完成後，你的服務 URL 會是：`https://your-service.zeabur.app`

測試命令：
```bash
curl https://your-service.zeabur.app/health
```

應該返回：`{"status":"healthy"}`

🎉 **你的 5 個 API Keys 已經準備好進行負載均衡了！**
