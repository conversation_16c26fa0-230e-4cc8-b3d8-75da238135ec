#!/bin/bash

# 獲取端口，默認為 8000
PORT=${PORT:-8000}

echo "🚀 Starting Gemini Balance application"
echo "📡 Host: 0.0.0.0"
echo "🔌 Port: $PORT"
echo "🔧 Environment: Docker"
echo "🗄️ Database: ${DATABASE_TYPE:-sqlite}"
echo "📊 API Keys count: $(echo $API_KEYS | jq length 2>/dev/null || echo 'N/A')"

# 检查必要的环境变量
if [ -z "$API_KEYS" ]; then
    echo "⚠️  Warning: API_KEYS not set, using default"
fi

if [ -z "$ALLOWED_TOKENS" ]; then
    echo "⚠️  Warning: ALLOWED_TOKENS not set, using default"
fi

echo "🎯 Starting uvicorn server..."

# 啟動應用
exec uvicorn app.main:app --host 0.0.0.0 --port $PORT --log-level info
