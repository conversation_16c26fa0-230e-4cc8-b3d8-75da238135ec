# Zeabur 部署指南

## 🚀 快速部署到 Zeabur

### 前提條件

1. ✅ GitHub 帳號
2. ✅ Zeabur 帳號（可用 GitHub 登入）
3. ✅ 你的 Gemini API Keys

### 步驟一：推送到 GitHub

1. **創建 GitHub 倉庫**
   - 登入 GitHub
   - 創建新的 repository（建議命名為 `gemini-balance`）
   - 設為 Public 或 Private（Zeabur 都支援）

2. **推送代碼**
   ```bash
   git init
   git add .
   git commit -m "Initial commit: Gemini Balance Proxy Service"
   git branch -M main
   git remote add origin https://github.com/YOUR_USERNAME/gemini-balance.git
   git push -u origin main
   ```

### 步驟二：在 Zeabur 部署

1. **登入 Zeabur**
   - 前往 [zeabur.com](https://zeabur.com)
   - 使用 GitHub 帳號登入

2. **創建新專案**
   - 點擊 "Create Project"
   - 選擇 "Deploy from GitHub"
   - 選擇你的 `gemini-balance` 倉庫

3. **配置環境變數**
   在 Zeabur 的環境變數設置中添加以下變數：

   ```
   DATABASE_TYPE=sqlite
   SQLITE_DATABASE=gemini_balance.db
   API_KEYS=["你的API_KEY1","你的API_KEY2","你的API_KEY3","你的API_KEY4","你的API_KEY5"]
   ALLOWED_TOKENS=["sk-gemini-proxy-token-123456"]
   AUTH_TOKEN=sk-gemini-proxy-token-123456
   TEST_MODEL=gemini-1.5-flash
   IMAGE_MODELS=["gemini-2.0-flash-exp"]
   SEARCH_MODELS=["gemini-2.0-flash-exp"]
   FILTERED_MODELS=["gemini-1.0-pro-vision-latest", "gemini-pro-vision", "chat-bison-001", "text-bison-001", "embedding-gecko-001"]
   BASE_URL=https://generativelanguage.googleapis.com/v1beta
   MAX_FAILURES=5
   MAX_RETRIES=3
   CHECK_INTERVAL_HOURS=1
   TIMEZONE=Asia/Shanghai
   TIME_OUT=300
   PROXIES=[]
   TOOLS_CODE_EXECUTION_ENABLED=false
   SHOW_SEARCH_LINK=true
   SHOW_THINKING_PROCESS=true
   URL_NORMALIZATION_ENABLED=false
   STREAM_OPTIMIZER_ENABLED=true
   STREAM_MIN_DELAY=0.016
   STREAM_MAX_DELAY=0.024
   STREAM_SHORT_TEXT_THRESHOLD=10
   STREAM_LONG_TEXT_THRESHOLD=50
   STREAM_CHUNK_SIZE=5
   FAKE_STREAM_ENABLED=false
   FAKE_STREAM_EMPTY_DATA_INTERVAL_SECONDS=5
   LOG_LEVEL=info
   AUTO_DELETE_ERROR_LOGS_ENABLED=true
   AUTO_DELETE_ERROR_LOGS_DAYS=7
   AUTO_DELETE_REQUEST_LOGS_ENABLED=false
   AUTO_DELETE_REQUEST_LOGS_DAYS=30
   SAFETY_SETTINGS=[{"category": "HARM_CATEGORY_HARASSMENT", "threshold": "OFF"}, {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "OFF"}, {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "OFF"}, {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "OFF"}, {"category": "HARM_CATEGORY_CIVIC_INTEGRITY", "threshold": "BLOCK_NONE"}]
   PORT=8000
   ```

4. **部署設置**
   - Zeabur 會自動檢測到 Python 專案
   - 確認使用 `requirements.txt` 安裝依賴
   - 啟動命令：`python -m uvicorn app.main:app --host 0.0.0.0 --port $PORT`

### 步驟三：測試部署

1. **獲取部署 URL**
   - Zeabur 會提供一個自動生成的 URL
   - 格式類似：`https://your-service.zeabur.app`

2. **測試 API**
   ```bash
   # 健康檢查
   curl https://your-service.zeabur.app/health
   
   # 測試模型列表
   curl -H "Authorization: Bearer sk-gemini-proxy-token-123456" \
        https://your-service.zeabur.app/v1/models
   
   # 測試聊天
   curl -X POST \
     -H "Authorization: Bearer sk-gemini-proxy-token-123456" \
     -H "Content-Type: application/json" \
     -d '{"model":"gemini-1.5-flash","messages":[{"role":"user","content":"Hello!"}]}' \
     https://your-service.zeabur.app/v1/chat/completions
   ```

### 步驟四：配置自定義域名（可選）

1. **在 Zeabur 中設置**
   - 前往專案設置
   - 添加自定義域名
   - 配置 DNS 記錄

2. **更新 n8n 配置**
   - 使用你的自定義域名替換 Zeabur 提供的 URL

## 🔧 使用你的雲端代理

### 在 n8n 中使用

1. **HTTP Request 節點配置**
   ```
   URL: https://your-service.zeabur.app/v1/chat/completions
   Method: POST
   Headers:
     Authorization: Bearer sk-gemini-proxy-token-123456
     Content-Type: application/json
   Body:
     {
       "model": "gemini-1.5-flash",
       "messages": [{"role": "user", "content": "{{ $json.message }}"}],
       "max_tokens": 1000
     }
   ```

### 在其他工具中使用

- **OpenAI 格式端點**: `https://your-service.zeabur.app/v1`
- **Gemini 格式端點**: `https://your-service.zeabur.app/v1beta`
- **API Key**: `sk-gemini-proxy-token-123456`

## 📊 監控和管理

- **管理面板**: `https://your-service.zeabur.app/keys_status`
- **健康檢查**: `https://your-service.zeabur.app/health`
- **Zeabur 日誌**: 在 Zeabur 控制台查看應用日誌

## 🎉 優勢

- ✅ **全球可訪問**: 不受本地網絡限制
- ✅ **自動擴展**: Zeabur 自動處理流量擴展
- ✅ **高可用性**: 雲端部署，24/7 可用
- ✅ **負載均衡**: 5 個 API Keys 自動輪詢
- ✅ **HTTPS 支援**: 自動 SSL 證書
- ✅ **簡單維護**: 通過 GitHub 推送更新代碼
