# 🚀 Zeabur 部署詳細步驟指南

## 📋 完整部署流程

### 前提條件檢查

- ✅ 已完成 GitHub 倉庫創建和代碼推送
- ✅ 擁有 5 個有效的 Gemini API Keys
- ✅ 有 Zeabur 帳號（可用 GitHub 登入）

### 第一步：登入 Zeabur

1. **前往 Zeabur**
   - 打開 [zeabur.com](https://zeabur.com)
   - 點擊 "Sign in with GitHub"
   - 授權 Zeabur 訪問你的 GitHub

2. **進入控制台**
   - 登入後會看到 Zeabur Dashboard
   - 點擊 "Create Project" 創建新專案

### 第二步：創建專案和服務

1. **創建專案**
   - 專案名稱：`gemini-balance`
   - 選擇地區：建議選擇 `Hong Kong` 或 `Singapore`（延遲較低）

2. **添加服務**
   - 點擊 "Add Service"
   - 選擇 "Git Repository"
   - 選擇你的 `gemini-balance` 倉庫
   - 點擊 "Deploy"

### 第三步：配置環境變數

在服務設置中，點擊 "Environment Variables"，添加以下變數：

#### 🔑 必需的環境變數

```bash
# 數據庫配置
DATABASE_TYPE=sqlite
SQLITE_DATABASE=gemini_balance.db

# API Keys（請替換為你的實際 API Keys）
API_KEYS=["AIzaSyAqcX0Y05HkcWaWeYeCRzHdaKngd1CoLhw","AIzaSyCfJXf641hGNYAfXmZVmxtBPQfxagcbcxE","AIzaSyBOYU7QHQTg_l7e5ZDHLMG76rJF0NH97-0","AIzaSyA8pJifiXAbo6uA6_s0pO2Ns6W9DoShKEg","AIzaSyB5MDpa1BGcfeA-EHt1zNA20LPz3aLuoTU"]

# 訪問令牌
ALLOWED_TOKENS=["sk-gemini-proxy-token-123456"]
AUTH_TOKEN=sk-gemini-proxy-token-123456

# 基本配置
TEST_MODEL=gemini-1.5-flash
BASE_URL=https://generativelanguage.googleapis.com/v1beta
PORT=8000
```

#### ⚙️ 可選的環境變數

```bash
# 模型配置
IMAGE_MODELS=["gemini-2.0-flash-exp"]
SEARCH_MODELS=["gemini-2.0-flash-exp"]
FILTERED_MODELS=["gemini-1.0-pro-vision-latest","gemini-pro-vision","chat-bison-001","text-bison-001","embedding-gecko-001"]

# 性能配置
MAX_FAILURES=5
MAX_RETRIES=3
CHECK_INTERVAL_HOURS=1
TIME_OUT=300

# 功能開關
TOOLS_CODE_EXECUTION_ENABLED=false
SHOW_SEARCH_LINK=true
SHOW_THINKING_PROCESS=true

# 日誌配置
LOG_LEVEL=info
AUTO_DELETE_ERROR_LOGS_ENABLED=true
AUTO_DELETE_ERROR_LOGS_DAYS=7
```

### 第四步：部署設置

1. **檢查構建設置**
   - Zeabur 會自動檢測到 Python 專案
   - 確認 Build Command：`pip install -r requirements.txt`
   - 確認 Start Command：`python -m uvicorn app.main:app --host 0.0.0.0 --port $PORT`

2. **等待部署完成**
   - 部署過程大約需要 2-5 分鐘
   - 可以在 "Logs" 標籤查看部署進度

### 第五步：獲取部署 URL

1. **查看服務 URL**
   - 部署完成後，在服務頁面會顯示 URL
   - 格式類似：`https://gemini-balance-xxx.zeabur.app`

2. **測試服務**
   ```bash
   # 健康檢查
   curl https://your-service.zeabur.app/health
   
   # 應該返回：{"status":"healthy"}
   ```

### 第六步：完整測試

#### 測試 1：模型列表
```bash
curl -H "Authorization: Bearer sk-gemini-proxy-token-123456" \
     https://your-service.zeabur.app/v1/models
```

#### 測試 2：聊天 API
```bash
curl -X POST \
  -H "Authorization: Bearer sk-gemini-proxy-token-123456" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gemini-1.5-flash",
    "messages": [{"role": "user", "content": "Hello! 請用中文回覆"}],
    "max_tokens": 100
  }' \
  https://your-service.zeabur.app/v1/chat/completions
```

#### 測試 3：管理面板
在瀏覽器中打開：`https://your-service.zeabur.app/keys_status`

### 第七步：配置自定義域名（可選）

1. **在 Zeabur 中設置**
   - 前往服務設置 > "Domains"
   - 點擊 "Add Domain"
   - 輸入你的域名（如：`api.yourdomain.com`）

2. **配置 DNS**
   - 在你的域名提供商處添加 CNAME 記錄
   - 指向 Zeabur 提供的目標地址

## 🎯 在 n8n 中使用

### HTTP Request 節點配置

```json
{
  "url": "https://your-service.zeabur.app/v1/chat/completions",
  "method": "POST",
  "headers": {
    "Authorization": "Bearer sk-gemini-proxy-token-123456",
    "Content-Type": "application/json"
  },
  "body": {
    "model": "gemini-1.5-flash",
    "messages": [
      {
        "role": "user",
        "content": "{{ $json.message }}"
      }
    ],
    "max_tokens": 1000,
    "temperature": 0.7
  }
}
```

## 🔧 故障排除

### 問題 1：部署失敗
- 檢查 requirements.txt 是否正確
- 查看 Zeabur 部署日誌
- 確認 Python 版本兼容性

### 問題 2：環境變數錯誤
- 確認 JSON 格式正確（API_KEYS 等）
- 檢查是否有特殊字符需要轉義

### 問題 3：API 調用失敗
- 驗證 API Keys 是否有效
- 檢查 ALLOWED_TOKENS 配置
- 查看服務日誌

## 🎉 完成！

恭喜！你現在擁有：

- ✅ **雲端 Gemini 代理服務**
- ✅ **5 個 API Keys 負載均衡**
- ✅ **全球可訪問的 HTTPS 端點**
- ✅ **自動擴展和高可用性**
- ✅ **完整的監控和管理功能**

你的 n8n workflow 現在可以愉快地使用 Google 的 LLM 了！🚀
