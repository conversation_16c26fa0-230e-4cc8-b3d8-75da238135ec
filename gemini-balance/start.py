#!/usr/bin/env python3
"""
Zeabur 部署啟動腳本
確保正確處理端口和環境變數
"""

import os
import sys
import uvicorn
from dotenv import load_dotenv

# 加載環境變數
load_dotenv()

# 添加當前目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """主啟動函數"""
    try:
        # 導入應用
        from app.core.application import create_app
        from app.log.logger import get_main_logger
        
        # 創建應用實例
        app = create_app()
        
        # 獲取日誌器
        logger = get_main_logger()
        
        # 獲取端口，優先使用環境變數
        port = int(os.getenv("PORT", 8000))
        host = os.getenv("HOST", "0.0.0.0")
        
        logger.info(f"🚀 Starting Gemini Balance Proxy Server...")
        logger.info(f"📡 Host: {host}")
        logger.info(f"🔌 Port: {port}")
        logger.info(f"🌍 Environment: {os.getenv('ENVIRONMENT', 'production')}")
        
        # 啟動服務器
        uvicorn.run(
            app,
            host=host,
            port=port,
            log_level="info",
            access_log=True,
            workers=1
        )
        
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
