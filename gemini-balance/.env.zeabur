# Zeabur 部署環境變數範例
# 複製這些變數到 Zeabur 的環境變數設置中

# 數據庫配置 - 使用 SQLite 簡化部署
DATABASE_TYPE=sqlite
SQLITE_DATABASE=gemini_balance.db

# API Keys 配置 - 請替換為你的實際 API Keys
API_KEYS=["AIzaSyAqcX0Y05HkcWaWeYeCRzHdaKngd1CoLhw","AIzaSyCfJXf641hGNYAfXmZVmxtBPQfxagcbcxE","AIzaSyBOYU7QHQTg_l7e5ZDHLMG76rJF0NH97-0","AIzaSyA8pJifiXAbo6uA6_s0pO2Ns6W9DoShKEg","AIzaSyB5MDpa1BGcfeA-EHt1zNA20LPz3aLuoTU"]

# 訪問令牌配置
ALLOWED_TOKENS=["sk-gemini-proxy-token-123456"]
AUTH_TOKEN=sk-gemini-proxy-token-123456

# 模型配置
TEST_MODEL=gemini-1.5-flash
IMAGE_MODELS=["gemini-2.0-flash-exp"]
SEARCH_MODELS=["gemini-2.0-flash-exp"]

# 過濾不需要的模型
FILTERED_MODELS=["gemini-1.0-pro-vision-latest", "gemini-pro-vision", "chat-bison-001", "text-bison-001", "embedding-gecko-001"]

# 基本配置
BASE_URL=https://generativelanguage.googleapis.com/v1beta
MAX_FAILURES=5
MAX_RETRIES=3
CHECK_INTERVAL_HOURS=1
TIMEZONE=Asia/Shanghai
TIME_OUT=300

# 代理配置（如果需要）
PROXIES=[]

# 功能開關
TOOLS_CODE_EXECUTION_ENABLED=false
SHOW_SEARCH_LINK=true
SHOW_THINKING_PROCESS=true
URL_NORMALIZATION_ENABLED=false

# 流式輸出優化
STREAM_OPTIMIZER_ENABLED=true
STREAM_MIN_DELAY=0.016
STREAM_MAX_DELAY=0.024
STREAM_SHORT_TEXT_THRESHOLD=10
STREAM_LONG_TEXT_THRESHOLD=50
STREAM_CHUNK_SIZE=5

# 假流式配置
FAKE_STREAM_ENABLED=false
FAKE_STREAM_EMPTY_DATA_INTERVAL_SECONDS=5

# 日誌配置
LOG_LEVEL=info
AUTO_DELETE_ERROR_LOGS_ENABLED=true
AUTO_DELETE_ERROR_LOGS_DAYS=7
AUTO_DELETE_REQUEST_LOGS_ENABLED=false
AUTO_DELETE_REQUEST_LOGS_DAYS=30

# 安全設置
SAFETY_SETTINGS=[{"category": "HARM_CATEGORY_HARASSMENT", "threshold": "OFF"}, {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "OFF"}, {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "OFF"}, {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "OFF"}, {"category": "HARM_CATEGORY_CIVIC_INTEGRITY", "threshold": "BLOCK_NONE"}]

# Zeabur 特定配置
PORT=8000
