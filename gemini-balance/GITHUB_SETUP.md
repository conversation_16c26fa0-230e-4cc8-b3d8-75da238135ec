# 🚀 GitHub 設置和推送指南

## 📋 你需要完成的步驟

### 第一步：創建 GitHub 倉庫

1. **登入 GitHub**
   - 前往 [github.com](https://github.com)
   - 使用你的帳號登入

2. **創建新倉庫**
   - 點擊右上角的 "+" 按鈕
   - 選擇 "New repository"
   - 倉庫名稱：`gemini-balance`
   - 描述：`Gemini API Load Balancer Proxy Service`
   - 設為 Public 或 Private（建議 Public，方便 Zeabur 部署）
   - **不要**勾選 "Add a README file"
   - **不要**勾選 "Add .gitignore"
   - **不要**勾選 "Choose a license"
   - 點擊 "Create repository"

### 第二步：連接本地倉庫到 GitHub

創建倉庫後，GitHub 會顯示設置指令。請在終端中執行：

```bash
# 進入專案目錄
cd gemini-balance

# 添加你的 GitHub 倉庫作為遠端
git remote add origin https://github.com/YOUR_USERNAME/gemini-balance.git

# 推送代碼到 GitHub
git push -u origin main
```

**請將 `YOUR_USERNAME` 替換為你的 GitHub 用戶名！**

### 第三步：驗證推送成功

推送成功後，你應該能在 GitHub 上看到：
- ✅ 所有專案檔案
- ✅ README.md
- ✅ requirements.txt
- ✅ app/ 目錄
- ✅ ZEABUR_DEPLOYMENT.md
- ✅ 其他配置檔案

## 🔧 如果遇到問題

### 問題 1：權限被拒絕
如果看到 "Permission denied" 錯誤：

1. **檢查 GitHub 用戶名**
   ```bash
   git remote -v
   ```
   確保 URL 中的用戶名是你的

2. **重新設置遠端**
   ```bash
   git remote remove origin
   git remote add origin https://github.com/YOUR_USERNAME/gemini-balance.git
   ```

### 問題 2：需要身份驗證
如果需要輸入密碼：

1. **使用 Personal Access Token**
   - 前往 GitHub Settings > Developer settings > Personal access tokens
   - 生成新的 token
   - 使用 token 作為密碼

2. **或者使用 SSH**
   ```bash
   git remote set-<NAME_EMAIL>:YOUR_USERNAME/gemini-balance.git
   ```

## ✅ 完成後的下一步

推送成功後，你就可以：

1. **在 Zeabur 部署**
   - 前往 [zeabur.com](https://zeabur.com)
   - 選擇你的 GitHub 倉庫進行部署

2. **查看部署指南**
   - 參考 `ZEABUR_DEPLOYMENT.md` 檔案
   - 按照步驟配置環境變數

## 🎯 當前狀態

✅ **已完成**：
- 專案檔案已準備好
- Git 倉庫已初始化
- 代碼已提交到本地
- 部署文檔已創建

⏳ **待完成**：
- 創建 GitHub 倉庫
- 推送代碼到 GitHub
- 在 Zeabur 部署

## 📞 需要幫助？

如果在設置過程中遇到任何問題，請告訴我：
1. 具體的錯誤訊息
2. 你執行的命令
3. 你的 GitHub 用戶名

我會幫你解決！🚀
